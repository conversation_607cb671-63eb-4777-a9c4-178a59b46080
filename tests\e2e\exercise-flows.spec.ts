import { test, expect } from '@playwright/test'
import { login, waitForLoadingComplete } from './helpers'
import { createConsoleErrorMonitor } from './helpers/critical-flow-utils'

// Mobile-first configuration
test.use({
  viewport: { width: 375, height: 667 }, // iPhone SE
  hasTouch: true,
  isMobile: true,
})

test.describe('@critical Exercise Flows - Navigation', () => {
  // No mocks - using real API with test credentials

  test('Navigate through workout flow - from program to exercise', async ({
    page,
    browserName,
  }) => {
    // Set longer timeout for Safari
    if (browserName === 'webkit') {
      test.setTimeout(60000)
    }
    // First login and navigate to a workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('/program', { timeout: 10000 })
    await waitForLoadingComplete(page)

    // Open workout with the correct button text
    const openButton = page.getByRole('button', { name: /open workout/i })
    await openButton.click()

    // Wait for navigation to workout page
    await page.waitForURL('/workout', { timeout: 10000 })
    await waitForLoadingComplete(page)

    // Wait for workout page to fully load
    await page.waitForTimeout(3000)

    // Click on the first exercise card using the correct selector
    const exerciseCard = page.locator('[data-testid="exercise-card"]').first()
    await expect(exerciseCard).toBeVisible({ timeout: 10000 })
    await exerciseCard.click()

    // Wait for navigation to exercise page
    await page.waitForURL(/\/exercise\/\d+/, { timeout: 10000 })

    // Monitor console for errors
    const errorMonitor = createConsoleErrorMonitor(page)

    // Wait for page to load
    await waitForLoadingComplete(page)

    // Wait for the save button to be visible using the correct selector
    const saveButton = page.locator('[data-testid="floating-save-button"]')
    await expect(saveButton).toBeVisible({ timeout: 10000 })

    // Verify the button text
    await expect(saveButton).toHaveText('Save set')

    // Verify we're on the exercise page
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/)

    // Verify no race condition errors during navigation
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })
})
